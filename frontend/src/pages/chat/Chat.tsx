/**
 * Chat Page - Playground Interface
 * Modern chat interface with code display panel
 */

import React, { useEffect, useRef, useState } from 'react';
import {
  Trash2,
  Code2,
  Co<PERSON>,
  ChevronRight,
  RefreshCw,
  <PERSON><PERSON>,
  User,
  Sparkles,
  Zap,
  Wifi,
  WifiOff
} from 'lucide-react';
import { useChat } from '../../hooks/useChat';
import ChatInput from './components/ChatInput';
import { Button } from '../../components';

const Chat: React.FC = () => {
  const { messages, isLoading, error, sendMessage, clearHistory, refreshHistory, isTyping, streamingResponse } = useChat();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [selectedMessage, setSelectedMessage] = useState<string | null>(null);
  const [showCodePanel, setShowCodePanel] = useState(false); // Start hidden
  const [useStreaming, setUseStreaming] = useState(false); // Default to normal chat (non-streaming)
  const [userTyping, setUserTyping] = useState(false); // Track user typing
  const [sendingMessage, setSendingMessage] = useState<string | null>(null); // Track message being sent
  const [showSendAnimation, setShowSendAnimation] = useState(false); // Control send animation

  // Format AI message content with better styling
  const formatAIMessage = (content: string) => {
    // Split content into lines and format
    const lines = content.split('\n');
    const formattedContent: React.ReactElement[] = [];

    lines.forEach((line, index) => {
      const trimmedLine = line.trim();

      if (trimmedLine === '') {
        formattedContent.push(<br key={`br-${index}`} />);
      } else if (trimmedLine.match(/^\d+\.\s+\*\*.*\*\*/)) {
        // Numbered list with bold items (e.g., "1. **SEE Bridge Course**")
        const match = trimmedLine.match(/^(\d+)\.\s+\*\*(.*?)\*\*(.*)$/);
        if (match) {
          formattedContent.push(
            <div key={index} className="flex items-start space-x-3 mb-3">
              <div className="flex-shrink-0 w-6 h-6 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                {match[1]}
              </div>
              <div>
                <span className="font-semibold text-gray-900">{match[2]}</span>
                <span className="text-gray-700">{match[3]}</span>
              </div>
            </div>
          );
        }
      } else if (trimmedLine.startsWith('**') && trimmedLine.endsWith('**')) {
        // Bold headers
        const text = trimmedLine.slice(2, -2);
        formattedContent.push(
          <div key={index} className="font-bold text-gray-900 mb-2 flex items-center">
            <Sparkles className="h-4 w-4 text-purple-500 mr-2" />
            {text}
          </div>
        );
      } else if (trimmedLine.includes('**')) {
        // Inline bold text
        const parts = trimmedLine.split(/\*\*(.*?)\*\*/g);
        formattedContent.push(
          <div key={index} className="mb-2">
            {parts.map((part, partIndex) =>
              partIndex % 2 === 1 ? (
                <span key={partIndex} className="font-semibold text-gray-900">{part}</span>
              ) : (
                <span key={partIndex} className="text-gray-700">{part}</span>
              )
            )}
          </div>
        );
      } else {
        // Regular text
        formattedContent.push(
          <div key={index} className="text-gray-700 mb-1">{trimmedLine}</div>
        );
      }
    });

    return <div className="space-y-1">{formattedContent}</div>;
  };

  // Check if any message has tool calls
  const hasToolCalls = messages.some(msg => msg.tools_used && msg.tools_used.length > 0);

  // Auto-show tools panel when tool calls are present
  useEffect(() => {
    if (hasToolCalls && !showCodePanel) {
      setShowCodePanel(true);
    }
  }, [hasToolCalls, showCodePanel]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isTyping]);

  const handleSendMessage = async (message: string) => {
    setUserTyping(false); // Stop user typing animation
    setSendingMessage(message); // Set the message being sent
    setShowSendAnimation(true); // Show send animation

    // Brief delay to show the send animation
    setTimeout(() => {
      setShowSendAnimation(false);
      setSendingMessage(null);
    }, 800);

    await sendMessage(message, useStreaming);
  };

  const handleClearHistory = async () => {
    if (window.confirm('Are you sure you want to clear all chat history? This will clear both local and server-side conversation memory.')) {
      try {
        await clearHistory();
      } catch (error) {
        console.error('Failed to clear history:', error);
        // Error is already handled in useChat hook
      }
    }
  };



  // Get tools used for a specific message
  const getToolsForMessage = (messageId: string) => {
    const message = messages.find(msg => msg.id === messageId);
    if (!message || !message.tools_used) {
      return [];
    }

    return message.tools_used.map(tool => ({
      name: tool.name,
      description: tool.description || `Tool: ${tool.name}`,
      input: tool.input || {},
      output: tool.output || 'Tool executed successfully',
      code: `# Tool: ${tool.name}
# Description: ${tool.description || `Execute ${tool.name} tool`}
# Input: ${JSON.stringify(tool.input, null, 2)}
# Output: ${tool.output || 'Tool executed successfully'}

def ${tool.name}(${Object.keys(tool.input || {}).join(', ')}):
    """${tool.description || `Execute ${tool.name} tool`}"""
    # Tool implementation would be here
    return result`,
      language: "python"
    }));
  };

  return (
    <div className="h-full flex bg-gray-50">
      {/* Chat Panel */}
      <div className="flex-1 flex flex-col bg-white">
        {/* Enhanced Chat Header */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-white to-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center shadow-lg">
                  <Bot className="h-6 w-6 text-white" />
                </div>
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                  EduMind AI
                </h1>
                <p className="text-sm text-gray-600 font-medium">
                  Your intelligent learning companion
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {/* Streaming Toggle */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setUseStreaming(!useStreaming)}
                title={useStreaming ? "Switch to Normal Chat" : "Switch to Streaming Chat"}
                className={`transition-colors ${
                  useStreaming
                    ? 'bg-green-100 text-green-700 hover:bg-green-200'
                    : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                }`}
              >
                {useStreaming ? (
                  <>
                    <Wifi className="h-4 w-4 mr-1" />
                    <span className="text-xs font-medium">Streaming</span>
                  </>
                ) : (
                  <>
                    <WifiOff className="h-4 w-4 mr-1" />
                    <span className="text-xs font-medium">Normal</span>
                  </>
                )}
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={refreshHistory}
                disabled={isLoading}
                title="Refresh chat history from server"
                className="hover:bg-purple-100 hover:text-purple-700 transition-colors"
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearHistory}
                disabled={messages.length === 0}
                className="hover:bg-red-100 hover:text-red-700 transition-colors"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
              {hasToolCalls && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowCodePanel(!showCodePanel)}
                  className={`transition-colors ${
                    showCodePanel
                      ? 'bg-purple-100 text-purple-700'
                      : 'hover:bg-purple-100 hover:text-purple-700'
                  }`}
                >
                  <Code2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Chat Messages */}
        <div className="flex-1 overflow-y-auto px-6 py-6 space-y-8 chat-scroll">
          {messages.length === 0 ? (
            <div className="text-center py-16">
              <div className="relative">
                <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                  <Bot className="h-10 w-10 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-400 rounded-full animate-pulse"></div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-3">
                Welcome to EduMind AI
              </h3>
              <p className="text-gray-600 text-lg max-w-md mx-auto">
                I'm here to help you with courses, programming, and answer any questions you have. Let's start chatting!
              </p>
              <div className="mt-6 flex justify-center space-x-2">
                <div className="px-4 py-2 bg-purple-100 text-purple-700 rounded-full text-sm font-medium">
                  Course Information
                </div>
                <div className="px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">
                  Programming Help
                </div>
                <div className="px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm font-medium">
                  General Questions
                </div>
              </div>
            </div>
          ) : (
            <>
              {messages.map((msg) => (
                <div key={msg.id} className="space-y-4 animate-fadeIn">
                  {/* User Message */}
                  <div className="flex justify-end items-start space-x-4 animate-slideInRight">
                    <div className="max-w-2xl"> {/* Reduced from max-w-4xl to max-w-2xl */}
                      <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-3 rounded-2xl rounded-br-lg shadow-lg break-words">
                        <p className="text-sm leading-relaxed whitespace-pre-wrap">{msg.message}</p>
                      </div>
                      <div className="flex items-center justify-end mt-2 space-x-2">
                        <p className="text-xs text-gray-500 font-medium">
                          You • {msg.timestamp.toLocaleTimeString()}
                        </p>
                        <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                      </div>
                    </div>
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg">
                        <User className="h-6 w-6 text-white" />
                      </div>
                      <div className="w-1 h-6 bg-gradient-to-b from-purple-500 to-transparent mx-auto mt-1"></div>
                    </div>
                  </div>

                  {/* Bot Response */}
                  {msg.response && (
                    <div className="flex justify-start items-start space-x-4 animate-slideInLeft">
                      <div className="flex-shrink-0">
                        <div className="w-10 h-10 bg-gradient-to-r from-emerald-400 to-cyan-400 rounded-full flex items-center justify-center shadow-lg">
                          <Bot className="h-6 w-6 text-white" />
                        </div>
                        <div className="w-1 h-6 bg-gradient-to-b from-emerald-400 to-transparent mx-auto mt-1"></div>
                      </div>
                      <div className="flex-1 max-w-3xl">
                        <div
                          className={`bg-white border-2 px-6 py-5 rounded-3xl rounded-tl-lg shadow-lg cursor-pointer transition-all duration-300 hover:shadow-xl break-words ${
                            msg.tools_used && msg.tools_used.length > 0
                              ? 'border-purple-200 hover:border-purple-300 hover:bg-purple-50'
                              : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                          }`}
                          onClick={() => setSelectedMessage(selectedMessage === msg.id ? null : msg.id)}
                        >
                          <div className="prose prose-sm max-w-none whitespace-pre-wrap">
                            {formatAIMessage(msg.response)}
                          </div>
                          <div className="flex items-center justify-between mt-4 pt-3 border-t border-gray-100">
                            <div className="flex items-center space-x-2">
                              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                              <p className="text-xs text-gray-500 font-medium">
                                AI Assistant • {msg.timestamp.toLocaleTimeString()}
                              </p>
                            </div>
                            {msg.tools_used && msg.tools_used.length > 0 && (
                              <div className="flex items-center space-x-2 bg-purple-100 px-3 py-1 rounded-full">
                                <Zap className="h-3 w-3 text-purple-600" />
                                <span className="text-xs text-purple-700 font-medium">
                                  {msg.tools_used.length} tool{msg.tools_used.length > 1 ? 's' : ''} used
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}

              {/* User Typing Animation - Integrated into chat flow */}
              {userTyping && (
                <div className="space-y-4 animate-fadeIn">
                  <div className="flex justify-end items-start space-x-4 animate-slideInRight">
                    <div className="max-w-2xl">
                      <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-3 rounded-2xl rounded-br-lg shadow-lg opacity-70">
                        <div className="flex items-center justify-center space-x-1">
                          <div className="w-2 h-2 bg-white rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                      </div>
                      <div className="flex items-center justify-end mt-2 space-x-2 opacity-60">
                        <p className="text-xs text-gray-500 font-medium">
                          You • typing
                        </p>
                        <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                      </div>
                    </div>
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg opacity-80">
                        <User className="h-6 w-6 text-white" />
                      </div>
                      <div className="w-1 h-6 bg-gradient-to-b from-purple-500 to-transparent mx-auto mt-1 opacity-60"></div>
                    </div>
                  </div>
                </div>
              )}

              {/* Send Message Animation - Shows message flowing from input to chat */}
              {showSendAnimation && sendingMessage && (
                <div className="space-y-4 animate-fadeIn">
                  <div className="flex justify-end items-start space-x-4 animate-slideInUp">
                    <div className="max-w-2xl">
                      <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-3 rounded-2xl rounded-br-lg shadow-lg transform transition-all duration-800 ease-out animate-pulse">
                        <div className="animate-slideUp opacity-90">
                          {sendingMessage}
                        </div>
                      </div>
                      <div className="flex items-center justify-end mt-2 space-x-2 opacity-60">
                        <p className="text-xs text-gray-500 font-medium">
                          You • sending
                        </p>
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      </div>
                    </div>
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg">
                        <User className="h-6 w-6 text-white" />
                      </div>
                      <div className="w-1 h-6 bg-gradient-to-b from-purple-500 to-transparent mx-auto mt-1 opacity-60"></div>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}

          {/* Enhanced Typing Indicator */}
          {isTyping && (
            <div className="flex justify-start items-start space-x-4 animate-slideInLeft">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-gradient-to-r from-emerald-400 to-cyan-400 rounded-full flex items-center justify-center shadow-lg animate-pulse">
                  <Bot className="h-6 w-6 text-white" />
                </div>
                <div className="w-1 h-6 bg-gradient-to-b from-emerald-400 to-transparent mx-auto mt-1"></div>
              </div>
              <div className="bg-white border-2 border-gray-200 px-6 py-4 rounded-3xl rounded-tl-lg shadow-lg">
                {streamingResponse ? (
                  <div className="prose prose-sm max-w-none">
                    {formatAIMessage(streamingResponse)}
                    <div className="inline-block w-2 h-4 bg-purple-500 animate-pulse ml-1"></div>
                  </div>
                ) : (
                  <div className="flex items-center space-x-3">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                    <span className="text-sm text-gray-500 font-medium animate-pulse">EduMind AI is thinking...</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              <p className="text-sm">{error}</p>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>



        {/* Enhanced Chat Input - Transparent background to match chat window */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 animate-slideInUp">
          <div className="max-w-5xl mx-auto">
            <ChatInput
              onSendMessage={handleSendMessage}
              isLoading={isLoading}
              placeholder="Ask me anything about courses, programming, or get help with your questions..."
              onTyping={setUserTyping}
            />
          </div>
        </div>
      </div>

      {/* Code/Tools Panel - Only show when there are tool calls */}
      {showCodePanel && hasToolCalls && (
        <div className="w-96 bg-gray-50 border-l border-gray-200 flex flex-col">
          <div className="px-4 py-3 border-b border-gray-200 bg-white">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-900">Tools Used</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowCodePanel(false)}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto p-4">
            {selectedMessage ? (
              <div className="space-y-4">
                {getToolsForMessage(selectedMessage).map((tool, index) => (
                  <div key={index} className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                    <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium text-gray-900">{tool.name}</h4>
                          <p className="text-xs text-gray-500">{tool.description}</p>
                        </div>
                        <Button variant="ghost" size="sm">
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="p-4">
                      <pre className="text-xs text-gray-800 bg-gray-50 p-3 rounded border overflow-x-auto">
                        <code>{tool.code}</code>
                      </pre>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Code2 className="h-8 w-8 text-gray-400 mx-auto mb-3" />
                <p className="text-sm text-gray-500">
                  Click on a message to see the tools used
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Chat;
