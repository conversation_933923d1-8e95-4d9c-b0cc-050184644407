/**
 * Chat Hook
 * Manages chat state and provides chat functionality
 */

import { useState, useCallback, useEffect } from 'react';
import { chatService } from '../services';
import type { ChatMessage, ChatHistoryMessage } from '../services';

interface UseChatReturn {
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;
  sendMessage: (message: string, useStreaming?: boolean) => Promise<void>;
  clearHistory: () => Promise<void>;
  refreshHistory: () => Promise<void>;
  isTyping: boolean;
  streamingResponse: string;
}

export const useChat = (): UseChatReturn => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [streamingResponse, setStreamingResponse] = useState('');

  // Load chat history from server on mount
  useEffect(() => {
    loadChatHistory();
  }, []);

  const loadChatHistory = async () => {
    try {
      setIsLoading(true);
      const serverHistory = await chatService.getServerChatHistory();

      // Convert server history to local format
      const convertedMessages: ChatMessage[] = [];
      for (let i = 0; i < serverHistory.messages.length; i += 2) {
        const humanMsg = serverHistory.messages[i];
        const aiMsg = serverHistory.messages[i + 1];

        if (humanMsg && humanMsg.type === 'human') {
          convertedMessages.push({
            id: `${Date.now()}-${i}`,
            message: humanMsg.content,
            response: aiMsg?.content || '',
            timestamp: humanMsg.timestamp || new Date(),
            thread_id: serverHistory.session_id, // This is actually user_id now
            user_id: serverHistory.session_id,   // This is actually user_id now
            tools_used: [] // Tools are tracked per message on server, but we'll show them in response
          });
        }
      }

      setMessages(convertedMessages);
    } catch (err) {
      console.warn('Failed to load server chat history, falling back to local:', err);
      // Fallback to local storage
      const localHistory = chatService.getChatHistory();
      setMessages(localHistory);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshHistory = useCallback(async (): Promise<void> => {
    await loadChatHistory();
  }, []);

  const sendMessage = useCallback(async (message: string, useStreaming: boolean = false): Promise<void> => {
    if (!message.trim()) return;

    setIsLoading(true);
    setError(null);
    setIsTyping(true);
    setStreamingResponse('');

    try {
      // Add user message immediately
      const userMessage: ChatMessage = {
        id: Date.now().toString(),
        message,
        response: '',
        timestamp: new Date(),
        thread_id: '',
        user_id: '',
        tools_used: [],
      };

      setMessages(prev => [...prev, userMessage]);

      if (useStreaming) {
        // Use streaming API
        await chatService.sendMessageStream(
          message,
          (chunk) => {
            // Handle streaming chunks
            if (chunk.type === 'chunk') {
              setStreamingResponse(prev => prev + (chunk.content || ''));
            }
          },
          (response) => {
            // Handle completion
            setMessages(prev => {
              const updated = [...prev];
              const lastMessage = updated[updated.length - 1];
              if (lastMessage) {
                lastMessage.response = response.response;
                lastMessage.thread_id = response.thread_id;
                lastMessage.user_id = response.user_id;
                lastMessage.tools_used = response.tools_used || [];
              }
              return updated;
            });
            setStreamingResponse('');
          },
          (error) => {
            // Handle streaming error
            setError(error);
            setStreamingResponse('');
          }
        );
      } else {
        // Use regular API
        const response = await chatService.sendMessage(message);

        // Update with response
        setMessages(prev => {
          const updated = [...prev];
          const lastMessage = updated[updated.length - 1];
          if (lastMessage) {
            lastMessage.response = response.response;
            lastMessage.thread_id = response.thread_id;
            lastMessage.user_id = response.user_id;
            lastMessage.tools_used = response.tools_used || [];
          }
          return updated;
        });
      }
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'Failed to send message');
      setStreamingResponse('');

      // Remove the failed message
      setMessages(prev => prev.slice(0, -1));
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  }, []);

  const clearHistory = useCallback(async (): Promise<void> => {
    try {
      // Clear both server and local history
      await chatService.clearServerChatHistory();
      await chatService.clearHistory(); // Also clear local storage
      setMessages([]);
      setError(null);
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'Failed to clear conversation');
    }
  }, []);

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    clearHistory,
    refreshHistory,
    isTyping,
    streamingResponse,
  };
};
